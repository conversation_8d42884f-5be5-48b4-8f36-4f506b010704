import React, { useState } from 'react';
import {
  Card,
  Form,
  Input,
  Button,
  Tabs,
  Typography,
  Space,
  Divider,
  Checkbox,
  message,
  Row,
  Col
} from 'antd';
import {
  UserOutlined,
  LockOutlined,
  MailOutlined,
  PhoneOutlined,
  EyeInvisibleOutlined,
  EyeTwoTone,
  WechatOutlined,
  Ali<PERSON>yOutlined,
  QqOutlined
} from '@ant-design/icons';
import styled from 'styled-components';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';

const { Title, Text, Link } = Typography;
const { TabPane } = Tabs;

const AuthContainer = styled.div`
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('/images/book-pattern.png') repeat;
    opacity: 0.1;
  }
  
  .auth-card {
    position: relative;
    z-index: 2;
    width: 100%;
    max-width: 480px;
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    overflow: hidden;
    
    .auth-header {
      text-align: center;
      padding: 40px 40px 20px 40px;
      background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
      
      .logo {
        width: 80px;
        height: 80px;
        margin: 0 auto 20px auto;
        background: linear-gradient(135deg, #667eea, #764ba2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: white;
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
      }
      
      .auth-title {
        font-size: 28px;
        font-weight: 700;
        margin-bottom: 8px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
      }
      
      .auth-subtitle {
        color: #8c8c8c;
        font-size: 16px;
      }
    }
    
    .auth-content {
      padding: 20px 40px 40px 40px;
      
      .auth-tabs {
        .ant-tabs-nav {
          margin-bottom: 32px;
          
          .ant-tabs-tab {
            font-size: 16px;
            font-weight: 600;
            padding: 12px 24px;
          }
        }
        
        .tab-content {
          .auth-form {
            .ant-form-item {
              margin-bottom: 20px;
              
              .ant-input-affix-wrapper {
                height: 48px;
                border-radius: 8px;
                border: 1px solid #d9d9d9;
                transition: all 0.3s ease;
                
                &:hover,
                &:focus-within {
                  border-color: #667eea;
                  box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
                }
                
                .ant-input {
                  font-size: 15px;
                }
                
                .anticon {
                  color: #8c8c8c;
                }
              }
            }
            
            .form-actions {
              margin-top: 32px;
              
              .submit-btn {
                width: 100%;
                height: 48px;
                font-size: 16px;
                font-weight: 600;
                border-radius: 8px;
                background: linear-gradient(135deg, #667eea, #764ba2);
                border: none;
                
                &:hover {
                  background: linear-gradient(135deg, #5a6fd8, #6a4190);
                  transform: translateY(-1px);
                  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                }
                
                &:disabled {
                  background: #f5f5f5;
                  color: #bfbfbf;
                  transform: none;
                  box-shadow: none;
                }
              }
            }
            
            .form-options {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin: 20px 0;
              
              .remember-me {
                .ant-checkbox-wrapper {
                  font-size: 14px;
                }
              }
              
              .forgot-password {
                color: #667eea;
                font-size: 14px;
                
                &:hover {
                  color: #5a6fd8;
                }
              }
            }
          }
          
          .social-login {
            margin-top: 32px;
            
            .divider-text {
              color: #8c8c8c;
              font-size: 14px;
              text-align: center;
              margin: 24px 0;
              position: relative;
              
              &::before,
              &::after {
                content: '';
                position: absolute;
                top: 50%;
                width: 100px;
                height: 1px;
                background: #f0f0f0;
              }
              
              &::before {
                left: 0;
              }
              
              &::after {
                right: 0;
              }
            }
            
            .social-buttons {
              display: flex;
              gap: 12px;
              
              .social-btn {
                flex: 1;
                height: 44px;
                border-radius: 8px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 18px;
                transition: all 0.3s ease;
                
                &.wechat {
                  background: #07c160;
                  border-color: #07c160;
                  color: white;
                  
                  &:hover {
                    background: #06ad56;
                    border-color: #06ad56;
                    transform: translateY(-1px);
                  }
                }
                
                &.alipay {
                  background: #1677ff;
                  border-color: #1677ff;
                  color: white;
                  
                  &:hover {
                    background: #0958d9;
                    border-color: #0958d9;
                    transform: translateY(-1px);
                  }
                }
                
                &.qq {
                  background: #12b7f5;
                  border-color: #12b7f5;
                  color: white;
                  
                  &:hover {
                    background: #0ea5e9;
                    border-color: #0ea5e9;
                    transform: translateY(-1px);
                  }
                }
              }
            }
          }
          
          .switch-mode {
            text-align: center;
            margin-top: 24px;
            color: #8c8c8c;
            font-size: 14px;
            
            .switch-link {
              color: #667eea;
              font-weight: 600;
              cursor: pointer;
              
              &:hover {
                color: #5a6fd8;
              }
            }
          }
        }
      }
    }
  }
  
  .back-home {
    position: absolute;
    top: 24px;
    left: 24px;
    z-index: 3;
    
    .back-btn {
      background: rgba(255, 255, 255, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.3);
      color: white;
      border-radius: 8px;
      backdrop-filter: blur(10px);
      
      &:hover {
        background: rgba(255, 255, 255, 0.3);
        border-color: rgba(255, 255, 255, 0.5);
        color: white;
      }
    }
  }
`;

interface AuthPageProps {}

const AuthPage: React.FC<AuthPageProps> = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login, register } = useAuthStore();
  
  const [activeTab, setActiveTab] = useState('login');
  const [loading, setLoading] = useState(false);
  const [loginForm] = Form.useForm();
  const [registerForm] = Form.useForm();

  // 获取重定向路径
  const from = (location.state as any)?.from?.pathname || '/';

  const handleLogin = async (values: any) => {
    try {
      setLoading(true);
      await login(values.username, values.password);
      message.success('登录成功');
      navigate(from, { replace: true });
    } catch (error: any) {
      message.error(error.message || '登录失败');
    } finally {
      setLoading(false);
    }
  };

  const handleRegister = async (values: any) => {
    try {
      setLoading(true);
      await register({
        username: values.username,
        email: values.email,
        password: values.password,
        phone: values.phone
      });
      message.success('注册成功，请登录');
      setActiveTab('login');
      loginForm.setFieldsValue({
        username: values.username
      });
    } catch (error: any) {
      message.error(error.message || '注册失败');
    } finally {
      setLoading(false);
    }
  };

  const handleSocialLogin = (provider: string) => {
    message.info(`${provider}登录功能开发中...`);
  };

  const handleForgotPassword = () => {
    message.info('忘记密码功能开发中...');
  };

  return (
    <AuthContainer>
      <div className="back-home">
        <Button 
          className="back-btn"
          onClick={() => navigate('/')}
        >
          返回首页
        </Button>
      </div>

      <Card className="auth-card">
        <div className="auth-header">
          <div className="logo">
            <UserOutlined />
          </div>
          <Title level={2} className="auth-title">收书卖书</Title>
          <Text className="auth-subtitle">让知识流转，让阅读更有价值</Text>
        </div>

        <div className="auth-content">
          <Tabs 
            activeKey={activeTab} 
            onChange={setActiveTab}
            className="auth-tabs"
            centered
          >
            <TabPane tab="登录" key="login">
              <div className="tab-content">
                <Form
                  form={loginForm}
                  className="auth-form"
                  onFinish={handleLogin}
                  autoComplete="off"
                >
                  <Form.Item
                    name="username"
                    rules={[
                      { required: true, message: '请输入用户名或邮箱' }
                    ]}
                  >
                    <Input
                      prefix={<UserOutlined />}
                      placeholder="用户名或邮箱"
                      size="large"
                    />
                  </Form.Item>

                  <Form.Item
                    name="password"
                    rules={[
                      { required: true, message: '请输入密码' }
                    ]}
                  >
                    <Input.Password
                      prefix={<LockOutlined />}
                      placeholder="密码"
                      size="large"
                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                    />
                  </Form.Item>

                  <div className="form-options">
                    <Form.Item name="remember" valuePropName="checked" noStyle>
                      <Checkbox className="remember-me">记住我</Checkbox>
                    </Form.Item>
                    <Link className="forgot-password" onClick={handleForgotPassword}>
                      忘记密码？
                    </Link>
                  </div>

                  <div className="form-actions">
                    <Button
                      type="primary"
                      htmlType="submit"
                      className="submit-btn"
                      loading={loading}
                    >
                      登录
                    </Button>
                  </div>
                </Form>

                <div className="social-login">
                  <div className="divider-text">或使用第三方登录</div>
                  <div className="social-buttons">
                    <Button 
                      className="social-btn wechat"
                      icon={<WechatOutlined />}
                      onClick={() => handleSocialLogin('微信')}
                    />
                    <Button 
                      className="social-btn alipay"
                      icon={<AlipayOutlined />}
                      onClick={() => handleSocialLogin('支付宝')}
                    />
                    <Button 
                      className="social-btn qq"
                      icon={<QqOutlined />}
                      onClick={() => handleSocialLogin('QQ')}
                    />
                  </div>
                </div>

                <div className="switch-mode">
                  还没有账号？
                  <span className="switch-link" onClick={() => setActiveTab('register')}>
                    立即注册
                  </span>
                </div>
              </div>
            </TabPane>

            <TabPane tab="注册" key="register">
              <div className="tab-content">
                <Form
                  form={registerForm}
                  className="auth-form"
                  onFinish={handleRegister}
                  autoComplete="off"
                >
                  <Form.Item
                    name="username"
                    rules={[
                      { required: true, message: '请输入用户名' },
                      { min: 3, message: '用户名至少3个字符' },
                      { max: 20, message: '用户名最多20个字符' },
                      { pattern: /^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, message: '用户名只能包含字母、数字、下划线和中文' }
                    ]}
                  >
                    <Input
                      prefix={<UserOutlined />}
                      placeholder="用户名"
                      size="large"
                    />
                  </Form.Item>

                  <Form.Item
                    name="email"
                    rules={[
                      { required: true, message: '请输入邮箱' },
                      { type: 'email', message: '请输入有效的邮箱地址' }
                    ]}
                  >
                    <Input
                      prefix={<MailOutlined />}
                      placeholder="邮箱"
                      size="large"
                    />
                  </Form.Item>

                  <Form.Item
                    name="phone"
                    rules={[
                      { required: true, message: '请输入手机号' },
                      { pattern: /^1[3-9]\d{9}$/, message: '请输入有效的手机号' }
                    ]}
                  >
                    <Input
                      prefix={<PhoneOutlined />}
                      placeholder="手机号"
                      size="large"
                    />
                  </Form.Item>

                  <Form.Item
                    name="password"
                    rules={[
                      { required: true, message: '请输入密码' },
                      { min: 6, message: '密码至少6个字符' },
                      { max: 20, message: '密码最多20个字符' }
                    ]}
                  >
                    <Input.Password
                      prefix={<LockOutlined />}
                      placeholder="密码"
                      size="large"
                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                    />
                  </Form.Item>

                  <Form.Item
                    name="confirmPassword"
                    dependencies={['password']}
                    rules={[
                      { required: true, message: '请确认密码' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('password') === value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('两次输入的密码不一致'));
                        },
                      }),
                    ]}
                  >
                    <Input.Password
                      prefix={<LockOutlined />}
                      placeholder="确认密码"
                      size="large"
                      iconRender={(visible) => (visible ? <EyeTwoTone /> : <EyeInvisibleOutlined />)}
                    />
                  </Form.Item>

                  <Form.Item
                    name="agreement"
                    valuePropName="checked"
                    rules={[
                      { 
                        validator: (_, value) =>
                          value ? Promise.resolve() : Promise.reject(new Error('请同意用户协议'))
                      }
                    ]}
                  >
                    <Checkbox>
                      我已阅读并同意
                      <Link onClick={() => message.info('用户协议开发中...')}>《用户协议》</Link>
                      和
                      <Link onClick={() => message.info('隐私政策开发中...')}>《隐私政策》</Link>
                    </Checkbox>
                  </Form.Item>

                  <div className="form-actions">
                    <Button
                      type="primary"
                      htmlType="submit"
                      className="submit-btn"
                      loading={loading}
                    >
                      注册
                    </Button>
                  </div>
                </Form>

                <div className="switch-mode">
                  已有账号？
                  <span className="switch-link" onClick={() => setActiveTab('login')}>
                    立即登录
                  </span>
                </div>
              </div>
            </TabPane>
          </Tabs>
        </div>
      </Card>
    </AuthContainer>
  );
};

export default AuthPage;
